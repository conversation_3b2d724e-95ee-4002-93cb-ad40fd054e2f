import * as admin from "firebase-admin";
import {
  APP_CONFIG_COLLECTION,
  APP_CONFIG_DOC_ID,
  BPS_DIVISOR,
  MARKETPLACE_REVENUE_USER_ID,
} from "../constants";
import { AppConfigEntity, UserEntity } from "../types";
import { safeDivide, safeMultiply, safeSubtract } from "../utils";
import {
  addFunds,
  updateUserBalance,
  validateSufficientFunds,
} from "./balance-service";

export async function getAppConfig(): Promise<AppConfigEntity> {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(APP_CONFIG_COLLECTION)
      .doc(APP_CONFIG_DOC_ID)
      .get();

    if (!doc.exists) {
      console.log("App config not found, using zero fees");
      return {
        deposit_fee: 0,
        withdrawal_fee: 0,
        referrer_fee: 0,
        cancel_order_fee: 0,
        purchase_fee: 0,
        buyer_lock_percentage: 0,
        seller_lock_percentage: 0,
        resell_purchase_fee: 0,
        resell_purchase_fee_for_seller: 0,
        min_deposit_amount: 0,
        min_withdrawal_amount: 0,
        max_withdrawal_amount: 0,
        min_secondary_market_price: 0,
        fixed_cancel_order_fee: 0,
      };
    }

    return doc.data() as AppConfigEntity;
  } catch (error) {
    console.error("Error getting app config:", error);
    throw error;
  }
}

export function calculateFeeAmount(amount: number, feeBps: number): number {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return safeDivide(safeMultiply(amount, feeBps), BPS_DIVISOR);
}

export async function getAdminUser(): Promise<UserEntity | null> {
  try {
    const db = admin.firestore();
    const adminQuery = await db
      .collection("users")
      .where("role", "==", "admin")
      .limit(1)
      .get();

    if (adminQuery.empty) {
      console.error("No admin user found");
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    console.error("Error finding admin user:", error);
    throw error;
  }
}

export async function applyFeeToMarketplaceRevenue(
  feeAmount: number,
  feeType: string
): Promise<void> {
  if (feeAmount <= 0) {
    return;
  }

  try {
    await addFunds(MARKETPLACE_REVENUE_USER_ID, feeAmount);
    console.log(
      `Applied ${feeType} fee of ${feeAmount} TON to marketplace revenue`
    );
  } catch (error) {
    console.error(
      `Error applying ${feeType} fee to marketplace revenue:`,
      error
    );
    throw error;
  }
}

export async function applyDepositFee(
  userId: string,
  depositAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.deposit_fee) {
      return depositAmount;
    }

    // Deposit fee is now a static TON value, not BPS
    const feeAmount = config.deposit_fee;
    if (feeAmount <= 0 || feeAmount >= depositAmount) {
      return depositAmount;
    }

    const netAmount = safeSubtract(depositAmount, feeAmount);

    await applyFeeToMarketplaceRevenue(feeAmount, "deposit");

    console.log(
      `Deposit fee applied: ${feeAmount} TON (static fee), net amount: ${netAmount} TON`
    );

    return netAmount;
  } catch (error) {
    console.error("Error applying deposit fee:", error);
    throw error;
  }
}

export async function applyCancelOrderFee(
  userId: string,
  orderAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.cancel_order_fee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(orderAmount, config.cancel_order_fee);
    if (feeAmount <= 0) {
      return 0;
    }

    await validateSufficientFunds({
      userId,
      amount: feeAmount,
      operation: "cancel order fee",
    });

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    await applyFeeToMarketplaceRevenue(feeAmount, "cancel_order");

    console.log(
      `Cancel order fee applied: ${feeAmount} TON (${config.cancel_order_fee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying cancel order fee:", error);
    throw error;
  }
}

export async function applyFixedCancelOrderFee(userId: string) {
  try {
    const config = await getAppConfig();
    if (!config?.fixed_cancel_order_fee) {
      return 0;
    }

    // Fixed cancel order fee is a static TON value, not BPS
    const feeAmount = config.fixed_cancel_order_fee;
    if (feeAmount <= 0) {
      return 0;
    }

    await validateSufficientFunds({
      userId,
      amount: feeAmount,
      operation: "fixed cancel order fee",
    });

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    await applyFeeToMarketplaceRevenue(feeAmount, "fixed_cancel_order");

    console.log(
      `Fixed cancel order fee applied: ${feeAmount} TON (static fee) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying fixed cancel order fee:", error);
    throw error;
  }
}

export async function applyPurchaseFee(
  userId: string,
  purchaseAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.purchase_fee) {
      return 0;
    }

    const feeAmount = calculateFeeAmount(purchaseAmount, config.purchase_fee);
    if (feeAmount <= 0) {
      return 0;
    }

    // Validate user has sufficient funds for purchase fee
    await validateSufficientFunds({
      userId,
      amount: feeAmount,
      operation: "purchase fee",
    });

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    await applyFeeToMarketplaceRevenue(feeAmount, "purchase");

    console.log(
      `Purchase fee applied: ${feeAmount} TON (${config.purchase_fee} BPS) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying purchase fee:", error);
    throw error;
  }
}

export async function applyPurchaseFeeWithReferral(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
}): Promise<{ totalFee: number; referralFee: number; marketplaceFee: number }> {
  const { buyerId, amount, referralId } = params;
  try {
    const config = await getAppConfig();
    if (!config?.purchase_fee) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, config.purchase_fee);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "purchase fee with referral",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId) {
      const db = admin.firestore();
      const referrerQuery = await db
        .collection("users")
        .where("tg_id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to config
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          console.log(
            `Using custom referral fee: ${referrerFeeRate} BPS for referrer ${referrerId}`
          );
        } else if (config.referrer_fee > 0) {
          // Use default referral fee from config
          referrerFeeRate = config.referrer_fee;
          console.log(
            `Using default referral fee: ${referrerFeeRate} BPS for referrer ${referrerId}`
          );
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFunds(referrerId, referralFeeAmount);

          console.log(
            `Referral fee applied: ${referralFeeAmount} TON (${referrerFeeRate} BPS) to referrer ${referrerId} (tg_id: ${referralId})`
          );
        }
      } else {
        console.log(
          `Referrer with tg_id ${referralId} not found, adding full fee to marketplace`
        );
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue(marketplaceFeeAmount, "purchase");
    }

    console.log(
      `Purchase fee with referral applied: Total: ${totalFeeAmount} TON, Referral: ${referralFeeAmount} TON, Marketplace: ${marketplaceFeeAmount} TON`
    );

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    console.error("Error applying purchase fee with referral:", error);
    throw error;
  }
}

export async function applyResellPurchaseFeeWithReferral(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  resellPurchaseFeeBPS: number;
  referrerFeeBPS: number;
}): Promise<{ totalFee: number; referralFee: number; marketplaceFee: number }> {
  const { buyerId, amount, referralId, resellPurchaseFeeBPS, referrerFeeBPS } =
    params;
  try {
    if (!resellPurchaseFeeBPS || resellPurchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, resellPurchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "resell purchase fee with referral",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId && referrerFeeBPS > 0) {
      const db = admin.firestore();
      const referrerQuery = await db
        .collection("users")
        .where("tg_id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to order fee
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          console.log(
            `Using custom referral fee: ${referrerFeeRate} BPS for referrer ${referrerId}`
          );
        } else {
          // Use referral fee from order
          referrerFeeRate = referrerFeeBPS;
          console.log(
            `Using order referral fee: ${referrerFeeRate} BPS for referrer ${referrerId}`
          );
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFunds(referrerId, referralFeeAmount);

          console.log(
            `Resell referral fee applied: ${referralFeeAmount} TON (${referrerFeeRate} BPS) to referrer ${referrerId} (tg_id: ${referralId})`
          );
        }
      } else {
        console.log(
          `Referrer with tg_id ${referralId} not found, adding full fee to marketplace`
        );
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue(
        marketplaceFeeAmount,
        "resell_purchase"
      );
    }

    console.log(
      `Resell purchase fee with referral applied: Total: ${totalFeeAmount} TON, Referral: ${referralFeeAmount} TON, Marketplace: ${marketplaceFeeAmount} TON`
    );

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    console.error("Error applying resell purchase fee with referral:", error);
    throw error;
  }
}

export async function applyPurchaseFeeWithReferralFromOrder(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  purchaseFeeBPS: number;
  referrerFeeBPS: number;
}): Promise<{ totalFee: number; referralFee: number; marketplaceFee: number }> {
  const { buyerId, amount, referralId, purchaseFeeBPS, referrerFeeBPS } =
    params;
  try {
    if (!purchaseFeeBPS || purchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "purchase fee with referral from order",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId && referrerFeeBPS > 0) {
      const db = admin.firestore();
      const referrerQuery = await db
        .collection("users")
        .where("tg_id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to order fee
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          console.log(
            `Using custom referral fee: ${referrerFeeRate} BPS for referrer ${referrerId}`
          );
        } else {
          // Use referral fee from order
          referrerFeeRate = referrerFeeBPS;
          console.log(
            `Using order referral fee: ${referrerFeeRate} BPS for referrer ${referrerId}`
          );
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFunds(referrerId, referralFeeAmount);

          console.log(
            `Purchase referral fee applied: ${referralFeeAmount} TON (${referrerFeeRate} BPS) to referrer ${referrerId} (tg_id: ${referralId})`
          );
        }
      } else {
        console.log(
          `Referrer with tg_id ${referralId} not found, adding full fee to marketplace`
        );
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue(marketplaceFeeAmount, "purchase");
    }

    console.log(
      `Purchase fee with referral from order applied: Total: ${totalFeeAmount} TON, Referral: ${referralFeeAmount} TON, Marketplace: ${marketplaceFeeAmount} TON`
    );

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    console.error(
      "Error applying purchase fee with referral from order:",
      error
    );
    throw error;
  }
}

export async function applyWithdrawFee(
  userId: string,
  withdrawAmount: number
): Promise<number> {
  try {
    const config = await getAppConfig();
    if (!config?.withdrawal_fee) {
      return 0;
    }

    // Withdraw fee is now a static TON value, not BPS
    const feeAmount = config.withdrawal_fee;
    if (feeAmount <= 0 || feeAmount >= withdrawAmount) {
      return 0;
    }

    await applyFeeToMarketplaceRevenue(feeAmount, "withdrawal");

    console.log(
      `Withdrawal fee applied: ${feeAmount} TON (static fee) to user ${userId}`
    );

    return feeAmount;
  } catch (error) {
    console.error("Error applying withdrawal fee:", error);
    throw error;
  }
}
